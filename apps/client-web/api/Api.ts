/* eslint-disable */
/* tslint:disable */
// @ts-nocheck
/*
 * ---------------------------------------------------------------
 * ## THIS FILE WAS GENERATED VIA SWAGGER-TYPESCRIPT-API        ##
 * ##                                                           ##
 * ## AUTHOR: acacode                                           ##
 * ## SOURCE: https://github.com/acacode/swagger-typescript-api ##
 * ---------------------------------------------------------------
 */

import {
  KycOnboardingMultipartFormData,
  ResponseResultListCountryDTO,
  ResponseResultListIdentityDocumentTypeDTO,
  ResponseResultObject,
  ResponseResultUserProfileDTO,
  ResponseResultVoid,
  SubmitCryptoDepositRequest,
  SubmitFiatDepositMultipartFormData,
} from "./data-contracts";
import { ContentType, HttpClient, RequestParams } from "./http-client";

export class Api<
  SecurityDataType = unknown,
> extends HttpClient<SecurityDataType> {
  /**
   * No description
   *
   * @tags V1KycOnboardingController
   * @name Submit
   * @summary Upload KYC documents and metadata
   * @request POST:/api/v1/kyc/onboarding
   */
  submit = (data: KycOnboardingMultipartFormData, params: RequestParams = {}) =>
    this.request<
      ResponseResultVoid,
      (ResponseResultVoid | ResponseResultObject) | ResponseResultObject
    >({
      path: `/api/v1/kyc/onboarding`,
      method: "POST",
      body: data,
      type: ContentType.FormData,
      format: "json",
      ...params,
    });
  /**
   * No description
   *
   * @tags V1DepositController
   * @name SubmitFiatDeposit
   * @summary Submit a FIAT deposit request
   * @request POST:/api/v1/deposits/submit/fiat
   */
  submitFiatDeposit = (
    data: SubmitFiatDepositMultipartFormData,
    params: RequestParams = {},
  ) =>
    this.request<
      ResponseResultVoid,
      (ResponseResultVoid | ResponseResultObject) | ResponseResultObject
    >({
      path: `/api/v1/deposits/submit/fiat`,
      method: "POST",
      body: data,
      type: ContentType.FormData,
      format: "json",
      ...params,
    });
  /**
   * No description
   *
   * @tags V1DepositController
   * @name SubmitCryptoDeposit
   * @request POST:/api/v1/deposits/submit/crypto
   */
  submitCryptoDeposit = (
    data: SubmitCryptoDepositRequest,
    params: RequestParams = {},
  ) =>
    this.request<
      ResponseResultVoid,
      (ResponseResultVoid | ResponseResultObject) | ResponseResultObject
    >({
      path: `/api/v1/deposits/submit/crypto`,
      method: "POST",
      body: data,
      type: ContentType.Json,
      format: "json",
      ...params,
    });
  /**
   * No description
   *
   * @tags V1UserController
   * @name GetUserProfile
   * @request GET:/api/v1/user/profile
   */
  getUserProfile = (params: RequestParams = {}) =>
    this.request<
      ResponseResultUserProfileDTO,
      (ResponseResultVoid | ResponseResultObject) | ResponseResultObject
    >({
      path: `/api/v1/user/profile`,
      method: "GET",
      format: "json",
      ...params,
    });
  /**
   * No description
   *
   * @tags V1KycOnboardingController
   * @name GetIdentityDocumentTypes
   * @request GET:/api/v1/kyc/onboarding/document/identity/types
   */
  getIdentityDocumentTypes = (params: RequestParams = {}) =>
    this.request<
      ResponseResultListIdentityDocumentTypeDTO,
      (ResponseResultVoid | ResponseResultObject) | ResponseResultObject
    >({
      path: `/api/v1/kyc/onboarding/document/identity/types`,
      method: "GET",
      format: "json",
      ...params,
    });
  /**
   * No description
   *
   * @tags V1KycOnboardingController
   * @name GetCountries
   * @request GET:/api/v1/kyc/onboarding/countries
   */
  getCountries = (params: RequestParams = {}) =>
    this.request<
      ResponseResultListCountryDTO,
      (ResponseResultVoid | ResponseResultObject) | ResponseResultObject
    >({
      path: `/api/v1/kyc/onboarding/countries`,
      method: "GET",
      format: "json",
      ...params,
    });
  /**
   * No description
   *
   * @tags V1AuthenticationController
   * @name PreLogin
   * @request GET:/api/v1/auth/pre-login
   */
  preLogin = (params: RequestParams = {}) =>
    this.request<
      any,
      void | (ResponseResultVoid | ResponseResultObject) | ResponseResultObject
    >({
      path: `/api/v1/auth/pre-login`,
      method: "GET",
      ...params,
    });
}
