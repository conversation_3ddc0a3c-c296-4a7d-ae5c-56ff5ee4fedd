"use client";
import { useState } from "react";
import { Button } from "@radix-ui/themes";
import { Container } from "@/ui-components/layout/container";
import { ArrowLeftIcon } from "@radix-ui/react-icons";
import { DepositMethod } from "./constants";
import { SelectMethod } from "./select-method";
import { StepTitle } from "../../_components/form-layout";
import { BankForm } from "./bank-form";
import { CryptoForm } from "./crypto-form";
import { Link } from "@/i18n/navigation";
import { InfoLayout } from "@/ui-components/info-layout";
import { useTranslations } from "next-intl";

enum DepositStep {
  SelectMethod = 0,
  FormFill = 1,
  Success = 2,
}

export const DepositSteps = () => {
  const t = useTranslations("Portal.Deposit");
  const [currentStep, setCurrentStep] = useState(DepositStep.SelectMethod);
  const [depositMethod, setDepositMethod] = useState<string>(
    DepositMethod.BankTransfer,
  );

  const goPrevious = () => setCurrentStep((prev) => Math.max(0, prev - 1));
  const goNext = () => setCurrentStep((prev) => Math.min(4, prev + 1));

  const returnToDashboardButton = (
    <Link href="/dashboard">
      <Button
        asChild
        variant="soft"
        radius="full"
        color="gray"
        className="w-full"
      >
        <span>
          <ArrowLeftIcon className="shrink-0" /> {t("returnToDashboard")}
        </span>
      </Button>
    </Link>
  );

  const renderForm = () => {
    if (currentStep === DepositStep.SelectMethod) {
      return (
        <>
          <StepTitle title={t("selectDepositMethod")} />
          <SelectMethod
            depositMethod={depositMethod}
            setDepositMethod={setDepositMethod}
            goNext={goNext}
          />
        </>
      );
    }

    if (currentStep === DepositStep.FormFill) {
      return (
        <>
          <StepTitle title={t("followStepsToComplete")} />
          {depositMethod === DepositMethod.BankTransfer && (
            <BankForm goNext={goNext} />
          )}
          {depositMethod === DepositMethod.CryptoCurrency && (
            <CryptoForm goNext={goNext} />
          )}
        </>
      );
    }
  };

  if (currentStep === DepositStep.Success) {
    return (
      <InfoLayout
        className="py-20 md:py-10 xl:py-20"
        icon="/graphics/orange/pending.png"
        iconAlt="verifying deposit"
        title={t("verifyingDeposit")}
        description={t("depositSubmittedDescription")}
      >
        <div className="flex flex-col md:mx-auto">
          {returnToDashboardButton}
        </div>
      </InfoLayout>
    );
  }

  return (
    <Container className="py-6 md:py-10">
      <div className="col-span-full md:col-start-2 md:col-span-10 mb-6">
        {currentStep === 0 ? (
          <div className="flex mr-auto">{returnToDashboardButton}</div>
        ) : (
          <Button
            variant="soft"
            radius="full"
            color="gray"
            onClick={goPrevious}
          >
            <ArrowLeftIcon className="shrink-0" /> {t("previousPage")}
          </Button>
        )}
      </div>
      {renderForm()}
    </Container>
  );
};
